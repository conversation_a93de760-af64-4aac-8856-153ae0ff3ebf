/**
 * Unity Android构建自定义Launcher Gradle模板
 * 
 * 此模板用于解决Native库冲突问题，特别是libopenxr_loader.so冲突
 * 配置pickFirst策略来处理重复的OpenXR库文件
 */

apply plugin: 'com.android.application'

dependencies {
    implementation project(':unityLibrary')
    **DEPS**
}

android {
	namespace "**NAMESPACE**"
    ndkPath "**NDKPATH**"

    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        applicationId '**APPLICATIONID**'
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
    }

    aaptOptions {
        noCompress = ['.ress', '.resource', '.obb'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }**SIGN**

    lintOptions {
        abortOnError false
    }

    buildTypes {
        debug {
            minifyEnabled **MINIFY_DEBUG**
            proguardFiles getDefaultProguardFile('proguard-android.txt')**SIGNCONFIG**
            jniDebuggable true
        }
        release {
            minifyEnabled **MINIFY_RELEASE**
            proguardFiles getDefaultProguardFile('proguard-android.txt')**SIGNCONFIG**
        }**PACKAGING_OPTIONS**
    }

    // 解决Native库冲突的关键配置
    packagingOptions {
        // 处理libopenxr_loader.so冲突 - 优先使用第一个找到的版本
        pickFirst 'lib/arm64-v8a/libopenxr_loader.so'
        pickFirst 'lib/armeabi-v7a/libopenxr_loader.so'
        pickFirst 'lib/x86/libopenxr_loader.so'
        pickFirst 'lib/x86_64/libopenxr_loader.so'
        
        // 处理其他可能的OpenXR相关库冲突
        pickFirst 'lib/arm64-v8a/libunity.so'
        pickFirst 'lib/armeabi-v7a/libunity.so'
        pickFirst 'lib/x86/libunity.so'
        pickFirst 'lib/x86_64/libunity.so'
        
        // 排除不需要的文件
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = false
        }
        abi {
            enableSplit = true
        }
    }
}**SPLITS**
**BUILT_APK_LOCATION**
**LAUNCHER_SOURCE_BUILD_SETUP**
