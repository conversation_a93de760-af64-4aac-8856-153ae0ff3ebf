%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e2b12afd4d27418a9cfb2823fe2b9ff3, type: 3}
  m_Name: XRSimulationRuntimeSettings
  m_EditorClassIdentifier: 
  m_EnvironmentLayer: 30
  m_EnvironmentScanParams:
    m_MinimumRescanTime: 0.1
    m_DeltaCameraDistanceToRescan: 0.025
    m_DeltaCameraAngleToRescan: 4
    m_RaysPerCast: 10
    m_MaximumHitDistance: 12
    m_MinimumHitDistance: 0.05
  m_PlaneFindingParams:
    m_MinimumPlaneUpdateTime: 0.13
    m_MinPointsPerSqMeter: 30
    m_MinSideLength: 0.11
    m_InLayerMergeDistance: 0.2
    m_CrossLayerMergeDistance: 0.05
    m_CheckEmptyArea: 0
    m_AllowedEmptyAreaCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_PointUpdateDropoutRate: 0.4
    m_NormalToleranceAngle: 15
    m_VoxelSize: 0.1
  m_TrackedImageDiscoveryParams:
    m_TrackingUpdateInterval: 0.09
  m_EnvironmentProbeDiscoveryParams:
    m_MinUpdateTime: 0.2
    m_MaxDiscoveryDistance: 3
    m_DiscoveryDelayTime: 1
    m_CubemapFaceSize: 16
  m_AnchorDiscoveryParams:
    m_MinTimeUntilUpdate: 0.2
  m_UseXRay: 1
  m_FlipXRayDirection: 0
