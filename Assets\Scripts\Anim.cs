using System;
using UnityEngine;
using UnityEngine.UI;

[ExecuteInEditMode]
public class Anim : MonoBehaviour
{

    //private bool 
    public AnimPosition Pos;
    public AnimRotation Rot;
    public AnimSize RTSize;
    public AnimValue AValue;
    [SerializeField]
    public AnimScale Scale;
    public AnimColor Color;
    public AnimAlpha Alpha;
    public RectTransform m_rt;
    public Action OnComplite;
    private void Awake()
    {
    }

    [ExecuteInEditMode]
    private void Update()
    {
        bool enable = false;
        if (Pos != null && Pos.Enable)
        {
            enable = Pos.Update(this, m_rt);
        }

        if (Rot != null && Rot.Enable)
        {
            enable = Rot.Update(this, m_rt);
        }

        if (Scale != null && Scale.Enable)
        {
            enable = Scale.Update(this, m_rt);
        }

        if (Color != null && Color.Enable)
        {
            enable = Color.Update(this, m_rt);
        }

        if (Alpha != null && Alpha.Enable)
        {
            enable = Alpha.Update(this, m_rt);
        }

        if (RTSize != null && RTSize.Enable)
        {
            enable = RTSize.Update(this, m_rt);
        }

        if (AValue != null && AValue.Enable)
        {
            enable = AValue.Update(this, m_rt);
        }
        enabled = enable;
        if (!enable)
        {
            //动画播放结束.
            OnComplite?.Invoke();
        }
    }
}

[Serializable]
public class AnimParamBase
{
    public bool Enable = false;
    public float CurTime = 0;
    public AnimationCurve AnimCurve;
    public bool Pause = false;
    public WrapMode WrapMode = WrapMode.Default;
    public float DurationTime;
    public Action OnFinish;
    public Action<Anim, float> OnProgress;

    private bool m_InPingPongRevert = false;

    private void PingPongTime()
    {
        float tagCurrentTime = CurTime;
        if (m_InPingPongRevert)
        {
            tagCurrentTime -= Time.deltaTime;
        }
        else
        {
            tagCurrentTime += Time.deltaTime;
        }

        if (tagCurrentTime < 0)
        {

            m_InPingPongRevert = false;//要往正面走了.
            CurTime = Time.deltaTime + tagCurrentTime;
        }
        else if (tagCurrentTime > DurationTime)
        {
            m_InPingPongRevert = true;//要往正面走了.
            CurTime = DurationTime - (tagCurrentTime - DurationTime);
        }
        else
        {
            CurTime = tagCurrentTime;
        }

    }

    public bool Update(Anim go, RectTransform rt)
    {
        if (!Enable)
        {
            return false;
        }
        if (Pause)
        {
            return false;
        }
        //switch (WrapMode)
        //{
        //    case WrapMode.Default:
        //    case WrapMode.ClampForever:
        //    case WrapMode.Once:
        //        CurTime += Time.deltaTime;
        //        break;
        //    case WrapMode.Loop:
        //        break;
        //    case WrapMode.PingPong:
        //        break;


        //    default:
        //        break;
        //}



        float p = GetAnimPercent();
        float valuePercent = p;
        if (AnimCurve != null && AnimCurve.keys.Length > 0)
        {
            valuePercent = AnimCurve.Evaluate(p);
        }

        OnUpdate(go.gameObject, rt, valuePercent);


        if (p >= 1)
        {
            if (WrapMode == WrapMode.Loop)
            {
                CurTime = 0;
                return Enable;
            }
            if (WrapMode == WrapMode.PingPong)
            {
                PingPongTime();
                return Enable;

            }

            Enable = false;
            OnProgress?.Invoke(go, 1);
            OnFinish?.Invoke();
        }
        else
        {

            if (WrapMode == WrapMode.PingPong)
            {
                PingPongTime();
            }
            else
            {
                CurTime += Time.deltaTime;
            }
            OnProgress?.Invoke(go, p);

        }


        //CurTime += Time.deltaTime;

        //if (p >= 1)
        //{
        //    if (WrapMode == WrapMode.Loop)
        //    {
        //        DurationTime = p = 0;
        //        return Enable;
        //    }
        //    Enable = false;
        //    OnProgress?.Invoke(go, 1);
        //    OnFinish?.Invoke();
        //}
        //else if (p < 0)
        //{

        //}
        //else
        //{
        //    OnProgress?.Invoke(go, p);

        //}
        return Enable;
    }

    protected float GetAnimPercent()
    {
        float percent = 1;
        if (DurationTime > 0)
        {
            percent = CurTime / DurationTime;
        }
        return percent;
    }
    protected virtual void OnUpdate(GameObject go, RectTransform rt, float percent) { }
}
[Serializable]
public class AnimPosition : AnimParamBase
{
    public bool UseWorld = false;
    public Vector3 Target;
    public Vector3 Start;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        //if (AnimCurve != null)
        //{
        //    float valuePercent = AnimCurve.Evaluate(percent);
        //    DoAnim.UpdateValue(go.transform, rt, UseWorld, (Target - Start) * valuePercent);
        //}
        //else
        //{
        DoAnim.UpdateValue(go.transform, rt, UseWorld, Vector3.LerpUnclamped(Start, Target, percent));
        //}
    }
}
[Serializable]
public class AnimRotation : AnimParamBase
{
    public bool UseWorld = false;
    public Quaternion Target;
    public Quaternion Start;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        if (UseWorld)
        {
            go.transform.rotation = Quaternion.LerpUnclamped(Start, Target, percent);
        }
        else
        {
            go.transform.localRotation = Quaternion.LerpUnclamped(Start, Target, percent);
        }

    }
}
[Serializable]

public class AnimScale : AnimParamBase
{
    public Vector3 Target;
    public Vector3 Start;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        go.transform.localScale = Vector3.LerpUnclamped(Start, Target, percent);
    }

    public static Vector3 Lerp(Vector3 a, Vector3 b, float t)
    {
        t = Mathf.Clamp01(t);
        return new Vector3(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t, a.z + (b.z - a.z) * t);
    }
}
[Serializable]

public class AnimColor : AnimParamBase
{
    public Color Target;
    public Color Start;
    public UnityEngine.UI.Graphic Grap;
    public SpriteRenderer SpRender;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        if (Grap != null)
        {
            Grap.color = Color.LerpUnclamped(Start, Target, percent);// attrItem.m_Color;

        }
        else if (SpRender != null)
        {
            SpRender.color = Color.LerpUnclamped(Start, Target, percent);// attrItem.m_Color;

        }
    }
}
[Serializable]

public class AnimAlpha : AnimParamBase
{
    public float Target;
    public float Start;
    public CanvasGroup CanvasGroup;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        CanvasGroup.alpha = Mathf.LerpUnclamped(Start, Target, percent);// attrItem.m_Color;
    }
}


[Serializable]
public class AnimSize : AnimParamBase
{
    public Vector2 Target;
    public Vector2 Start;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        rt.sizeDelta = Vector2.LerpUnclamped(Start, Target, percent);
        //if (AnimCurve != null)
        //{
        //    float valuePercent = AnimCurve.Evaluate(percent);
        //    DoAnim.UpdateValue(go.transform, rt, UseWorld, (Target - Start) * valuePercent);
        //}
        //else
        //{
        //}
    }
}

[Serializable]
public class AnimValue : AnimParamBase
{
    public float Target;
    public float Start;
    public Action<float> ValueCallback;

    protected override void OnUpdate(GameObject go, RectTransform rt, float percent)
    {
        var value = Start + (Target - Start * percent);
        ValueCallback?.Invoke(value);
        //if (AnimCurve != null)
        //{
        //    float valuePercent = AnimCurve.Evaluate(percent);
        //    DoAnim.UpdateValue(go.transform, rt, UseWorld, (Target - Start) * valuePercent);
        //}
        //else
        //{
        //}
    }
}
public static class DoAnim
{
    public static Anim DoPosition(this Transform t, Vector3 target, float time)
    {
        return t.DoPosition(target, null, time);
    }
    public static Anim DoPosition(this Transform t, Vector3 target, AnimationCurve ac, float time)
    {
        return t.DoPosition(target, false, ac, time);
    }
    public static Anim DoPosition(this Transform t, Vector3 target, bool worldPos, float time)
    {
        return t.DoPosition(target, worldPos, null, time);
    }
    public static Anim DoPosition(this Transform t, Vector3 target, bool worldPos, AnimationCurve ac, float time)
    {
        if (time <= 0)
        {
            UpdateValue(t, t.GetComponent<RectTransform>(), worldPos, target);
            return GetAnim(t);
        }

        var a = GetAnim(t);
        a.m_rt = t.GetComponent<RectTransform>();
        a.enabled = true;
        if (a.Pos == null)
        {
            a.Pos = new AnimPosition();
        }
        a.Pos.Enable = true;
        a.Pos.CurTime = 0;
        a.Pos.AnimCurve = ac;
        a.Pos.Pause = false;
        a.Pos.DurationTime = time;
        a.Pos.UseWorld = worldPos;
        a.Pos.Target = target;

        if (worldPos)
        {
            a.Pos.Start = a.transform.position;
        }
        else
        {
            if (a.m_rt != null)
            {
                a.Pos.Start = a.m_rt.anchoredPosition3D;
            }
            else
            {
                a.Pos.Start = a.transform.localPosition;
            }
        }

        return a;
    }


    public static Anim DoRotation(this Transform t, Quaternion target, float time)
    {
        return t.DoRotation(target, null, time);
    }
    public static Anim DoRotation(this Transform t, Quaternion target, AnimationCurve ac, float time)
    {
        return t.DoRotation(target, false, ac, time);

    }
    public static Anim DoRotation(this Transform t, Quaternion target, bool worldPos, float time)
    {
        return t.DoRotation(target, worldPos, null, time);
    }

    public static Anim DoRotation(this Transform t, Quaternion target, bool worldPos, AnimationCurve ac, float time)
    {
        if (time <= 0)
        {
            UpdateValue(t, worldPos, target);
            return GetAnim(t);
        }
        var a = GetAnim(t);
        a.enabled = true;
        if (a.Rot == null)
        {
            a.Rot = new AnimRotation();
        }
        a.Rot.Enable = true;
        a.Rot.CurTime = 0;
        a.Rot.AnimCurve = ac;
        a.Rot.Pause = false;
        a.Rot.DurationTime = time;
        a.Rot.UseWorld = worldPos;
        a.Rot.Target = target;
        if (worldPos)
        {
            a.Rot.Start = a.transform.rotation;

        }
        else
        {
            a.Rot.Start = a.transform.localRotation;
        }

        return a;
    }




    public static Anim DoScale(this Transform t, Vector3 target, float time)
    {
        return t.DoScale(target, null, time);
    }
    public static Anim DoScale(this Transform t, Vector3 target, AnimationCurve ac, float time)
    {
        var a = GetAnim(t);
        a.enabled = true;
        if (a.Scale == null)
        {

            a.Scale = new AnimScale();

        }
        a.Scale.Enable = true;
        a.Scale.CurTime = 0;
        a.Scale.AnimCurve = ac;
        a.Scale.Pause = false;
        a.Scale.DurationTime = time;
        a.Scale.Target = target;
        a.Scale.Start = t.localScale;
        return a;
    }


    public static void StopAnim(Transform t)
    {
        Anim anim = t.GetComponent<Anim>();
        if (anim != null)
        {
            anim.enabled = false;
        }
    }


    public static Anim DoColor(this Graphic t, Color target, float time)
    {
        return t.DoColor(target, null, time);
    }



    public static Anim DoColor(this SpriteRenderer t, Color target, float time)
    {
        return t.DoColor(target, null, time);
    }

    public static Anim DoColor(this SpriteRenderer t, Color target, AnimationCurve ac, float time)
    {
        var a = GetAnim(t.transform);
        a.enabled = true;
        if (a.Color == null)
        {
            a.Color = new AnimColor();
        }
        a.Color.Enable = true;
        a.Color.CurTime = 0;
        a.Color.AnimCurve = ac;
        a.Color.Pause = false;
        a.Color.DurationTime = time;
        a.Color.Target = target;
        a.Color.SpRender = t;
        a.Color.Start = t.color;

        return a;
    }
    public static Anim DoColor(this Graphic t, Color target, AnimationCurve ac, float time)
    {
        var a = GetAnim(t.transform);
        a.enabled = true;
        if (a.Color == null)
        {
            a.Color = new AnimColor();
        }
        a.Color.Enable = true;
        a.Color.CurTime = 0;
        a.Color.AnimCurve = ac;
        a.Color.Pause = false;
        a.Color.DurationTime = time;
        a.Color.Target = target;
        a.Color.Grap = t;
        a.Color.Start = t.color;

        return a;
    }

    public static Anim DoAlpha(this CanvasGroup t, float target, float time)
    {
        return t.DoAlpha(target, null, time);
    }

    public static Anim DoAlpha(this CanvasGroup t, float target, AnimationCurve ac, float time)
    {
        var a = GetAnim(t.transform);
        a.enabled = true;
        if (a.Alpha == null)
        {
            a.Alpha = new AnimAlpha();
        }
        a.Alpha.Enable = true;
        a.Alpha.CurTime = 0;
        a.Alpha.AnimCurve = ac;
        a.Alpha.Pause = false;
        a.Alpha.DurationTime = time;
        a.Alpha.Start = t.alpha;
        a.Alpha.Target = target;
        a.Alpha.CanvasGroup = t;
        return a;
    }

    public static Anim DoSize(this RectTransform t, Vector2 target, float time)
    {
        return t.DoSize(target, null, time);
    }
    public static Anim DoSize(this RectTransform t, Vector2 target, AnimationCurve ac, float time)
    {
        var a = GetAnim(t);
        a.enabled = true;
        if (a.RTSize == null)
        {
            a.RTSize = new AnimSize();
            a.m_rt = t;

        }
        a.RTSize.Enable = true;
        a.RTSize.CurTime = 0;
        a.RTSize.AnimCurve = ac;
        a.RTSize.Pause = false;
        a.RTSize.DurationTime = time;
        a.RTSize.Target = target;
        a.RTSize.Start = t.sizeDelta;
        return a;
    }


    public static Anim GetAnim(Transform t)
    {
        Anim anim = t.GetComponent<Anim>();
        if (anim == null)
        {
            anim = t.gameObject.AddComponent<Anim>();
        }
        return anim;
    }


    public static Anim DoValue(this Transform t, float start, float target, float time)
    {
        var a = GetAnim(t);
        a.enabled = true;
        if (a.AValue == null)
        {
            a.AValue = new AnimValue();
            //a.m_rt = comp.GetComponent<>R;

        }
        a.AValue.Enable = true;
        a.AValue.CurTime = 0;
        a.AValue.Pause = false;
        a.AValue.DurationTime = time;
        a.AValue.Target = target;
        a.AValue.Start = start;
        return a;
    }



    public static void UpdateValue(Transform go, RectTransform rt, bool useWorld, Vector3 pos)
    {
        if (useWorld)
        {
            go.position = pos;
        }
        else
        {
            if (rt != null)
            {
                rt.anchoredPosition3D = pos;
            }
            else
            {
                go.localPosition = pos;
            }
        }
    }
    public static void UpdateValue(Transform go, bool useWorld, Quaternion rot)
    {
        if (useWorld)
        {
            go.rotation = rot;
        }
        else
        {
            go.localRotation = rot;
        }
    }

}
