using RayNeo;
using RayNeo.API;
using UnityEngine;
using UnityEngine.UI;

public class ShareCameraCtrl : MonoBehaviour
{
    public RawImage m_RI;
    private XRCameraHandler m_CameraHandler;
    void Start()
    {
        //camera = new XRCamera("0");
        //camera.open();
        //uid = camera.startPreviewChannel(1920, 1080, XRImageFormat.kImageMemoryRGBA, this);

        //SimpleTouch.Instance.OnSimpleTap.AddListener(() =>
        //{
        //    Debug.Log("ShareCameraCtrl: stop Preview");
        //    StopPreview();
        //});

        //SimpleTouch.Instance.OnDoubleTap.AddListener(() =>
        //{
        //    Debug.Log("ShareCameraCtrl: start Preview");
        //    StopPreview();
        //});
        StartPreview();
    }

    public void StartPreview()
    {
        if (m_CameraHandler != null)
        {
            return;
        }
        m_CameraHandler = ShareCamera.OpenCamera(m_RI);
        //m_RI.rectTransform.sizeDelta = new Vector2(m_CameraHandler.width, m_CameraHandler.height);
    }

    public void StopPreview()
    {
        ShareCamera.CloseCamera(m_CameraHandler);
        m_CameraHandler = null;
    }
    // Update is called once per frame
    //void Update()
    //{
    //    //m_Info.UpdateT2d();
    //    //if (!m_UpdateImg)
    //    //{
    //    //    return;
    //    //}
    //    //lock (this)
    //    //{
    //    //    m_PushedImg.Apply();
    //    //    m_RI.texture = m_PushedImg;
    //    //}
    //    //if (!m_UpdateImg)
    //    //{
    //    //    return;
    //    //}
    //    //m_UpdateImg = false;
    //    //if (m_PushedImg != null)
    //    //{
    //    //    m_PushedImg.Apply();
    //    //}
    //}
}
