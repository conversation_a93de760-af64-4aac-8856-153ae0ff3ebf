%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8816880322207848714
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7843029077916904080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d6ccd3d0ef0f1d458e69421dccbdae1, type: 3}
  m_Name: ValveIndexControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Valve Index Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.valveindex
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7736826144617953982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7631610445787237455
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7472116295746553225
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Android
  m_EditorClassIdentifier: 
  features:
  - {fileID: 4430365041251592631}
  - {fileID: -706923455118722127}
  - {fileID: -2339271755237018433}
  - {fileID: -1315747822165708194}
  - {fileID: -5990017187633945550}
  - {fileID: -8816880322207848714}
  - {fileID: 5496497502572086330}
  - {fileID: 8050065369444944426}
  - {fileID: -7122664110012216327}
  - {fileID: -*******************}
  - {fileID: -2633270012010004690}
  - {fileID: 5161600758573582621}
  - {fileID: *******************}
  - {fileID: 8344000987944439175}
  - {fileID: -2332872182156362693}
  - {fileID: -3885511951946454636}
  - {fileID: -2138453597772751568}
  - {fileID: 2670407749627497393}
  - {fileID: -2069663391198323868}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
--- !u!114 &-7219273457781814760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7122664110012216327
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5990017187633945550
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5402225854779424294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3885511951946454636
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f7ea624e395f004c88a3d9fd71882f7, type: 3}
  m_Name: RayNeoControllerProfile Android
  m_EditorClassIdentifier: Unity.XR.RayNeo.OpenXR:UnityEngine.XR.OpenXR.Features.Interactions:RayNeoControllerProfile
  m_enabled: 1
  nameUi: RayNeo Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.rayneo
  openxrExtensionStrings: 
  company: RayNeo
  priority: 0
  required: 0
--- !u!114 &-3380718382223865224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3261494970390529301
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274c02963f889a64e90bc2e596e21d13, type: 3}
  m_Name: HTCViveControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: HTC Vive Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.htcvive
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2633270012010004690
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2612832842709921035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &-2339271755237018433
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: EmptyFeatureTemplate Android
  m_EditorClassIdentifier: Unity.XR.RayNeo.OpenXR:Unity.XR.RayNeo.OpenXR.ARDK:EmptyFeatureTemplate
  m_enabled: 0
  nameUi: Empty Feature Template
  version: 
  featureIdInternal: com.unity.openxr.feature.empty.feature.template
  openxrExtensionStrings: 
  company: RayNeo
  priority: 0
  required: 0
--- !u!114 &-2332872182156362693
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2138453597772751568
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: RayNeoSupportFeature Android
  m_EditorClassIdentifier: Unity.XR.RayNeo.OpenXR:Unity.XR.RayNeo.OpenXR.ARDK:RayNeoSupportFeature
  m_enabled: 1
  nameUi: RayNeo Support
  version: 
  featureIdInternal: com.unity.openxr.feature.rayneo.support
  openxrExtensionStrings: 
  company: RayNeo
  priority: 0
  required: 0
  OpenSLAMOnStart: 0
  ATWSupport: 0
--- !u!114 &-2069663391198323868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1982686550294371485
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1315747822165708194
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1040817402169429147
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Standalone
  m_EditorClassIdentifier: 
  features:
  - {fileID: 8024212706365939986}
  - {fileID: -2612832842709921035}
  - {fileID: -7219273457781814760}
  - {fileID: 2618516534228761780}
  - {fileID: -3380718382223865224}
  - {fileID: 4023499240898584372}
  - {fileID: -3261494970390529301}
  - {fileID: -7631610445787237455}
  - {fileID: -1982686550294371485}
  - {fileID: -*******************}
  - {fileID: -7736826144617953982}
  - {fileID: 1959669742698603058}
  - {fileID: 6046025599471813174}
  - {fileID: -*******************}
  - {fileID: 5794722300350521353}
  - {fileID: -5402225854779424294}
  - {fileID: -7843029077916904080}
  - {fileID: 7658985359647636721}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
--- !u!114 &-706923455118722127
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f0ebc320a151d3408ea1e9fce54d40e, type: 3}
  m_Name: OpenXR Package Settings
  m_EditorClassIdentifier: 
  Keys: 0100000007000000
  Values:
  - {fileID: -1040817402169429147}
  - {fileID: -7472116295746553225}
--- !u!114 &1959669742698603058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 761fdd4502cb7a84e9ec7a2b24f33f37, type: 3}
  m_Name: MicrosoftMotionControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Motion Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.microsoftmotioncontroller
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &2618516534228761780
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &2670407749627497393
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &3233062825248396982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a2512471149fe2e438b7a4014f3bb528, type: 3}
  m_Name: EmptyFeatureTemplate Android
  m_EditorClassIdentifier: Unity.XR.RayNeo.OpenXR:Unity.XR.RayNeo.OpenXR.ARDK:EmptyFeatureTemplate
  m_enabled: 0
  nameUi: Empty Feature Template
  version: 
  featureIdInternal: com.unity.openxr.feature.empty.feature.template
  openxrExtensionStrings: 
  company: RayNeo
  priority: 0
  required: 0
--- !u!114 &4023499240898584372
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e5315f812f023cf4ebf26f7e5d2d70f2, type: 3}
  m_Name: HPReverbG2ControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: HP Reverb G2 Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.hpreverb
  openxrExtensionStrings: XR_EXT_hp_mixed_reality_controller
  company: Unity
  priority: 0
  required: 0
--- !u!114 &4430365041251592631
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9ef793c31862a37448e907829482ef80, type: 3}
  m_Name: OculusQuestFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Oculus Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.oculusquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetQuest: 1
  targetQuest2: 1
--- !u!114 &5161600758573582621
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &5496497502572086330
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &5794722300350521353
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6046025599471813174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &7036169678310070626
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ea1f4c0202e06074095ab78be7ef762a, type: 3}
  m_Name: RayNeoSupportFeature Android
  m_EditorClassIdentifier: Unity.XR.RayNeo.OpenXR:Unity.XR.RayNeo.OpenXR.ARDK:RayNeoSupportFeature
  m_enabled: 1
  nameUi: RayNeo Support
  version: 
  featureIdInternal: com.unity.openxr.feature.rayneo.support
  openxrExtensionStrings: 
  company: RayNeo
  priority: 0
  required: 0
  OpenSLAMOnStart: 0
  ATWSupport: 0
--- !u!114 &7658985359647636721
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8024212706365939986
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8050065369444944426
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f647cc0545697264a9878224faada6d5, type: 3}
  m_Name: MetaQuestFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.metaquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetDevices:
  - visibleName: Quest
    manifestName: quest
    enabled: 1
  - visibleName: Quest 2
    manifestName: quest2
    enabled: 1
  - visibleName: Quest Pro
    manifestName: cambria
    enabled: 1
  - visibleName: Quest 3
    manifestName: eureka
    enabled: 1
  - visibleName: Quest 3S
    manifestName: quest3s
    enabled: 1
  forceRemoveInternetPermission: 0
  symmetricProjection: 0
  foveatedRenderingApi: 0
  systemSplashScreen: {fileID: 0}
  optimizeBufferDiscards: 1
  lateLatchingMode: 0
  lateLatchingDebug: 0
--- !u!114 &8344000987944439175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
