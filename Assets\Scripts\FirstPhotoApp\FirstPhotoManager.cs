/**
 * FirstPhotoManager.cs
 * 
 * 第一次拍照应用的主管理脚本
 * 负责监听输入（键盘、触摸）以手动触发第一次拍照。
 * 拍照后，检测照片中的AprilTag，并将照片以特定格式命名保存，
 * 用于发送给后端以获取任务列表。
 * 支持单击拍照和双击退出功能。
 */
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using RayNeo; // SimpleTouch for input
using System.Text;
using System.Linq;
using Sirenix.OdinInspector;
using AprilTag; // 添加AprilTag命名空间引用
using System;

public class FirstPhotoManager : MonoBehaviour
{
    [Header("依赖组件")]
    [SerializeField] private UIManager uiManager; // 简单UI管理器

    [Header("相机预览")]
    [SerializeField] private RawImage previewRawImage; // 用于显示相机预览的RawImage
    
    [Header("拍照按键设置")]
    public KeyCode captureKeyCode1 = KeyCode.JoystickButton1; 
    public KeyCode captureKeyCode2 = KeyCode.RightArrow;

    [Header("拍照设置")]
    [SerializeField] private bool showDebugInfo = true; // 是否显示调试信息

    private bool isCapturing = false; 
    private Texture2D capturedTexture;

    // 引用其他管理器
    private TemplatePhotoManager templatePhotoManager;
    private WebCameraManager cameraManager;
    private AprilTagDetector tagDetector;
    private bool componentsInitialized = false;

    void Start()
    {
        InitializeComponents();
    }
    
    /// <summary>
    /// 初始化所有需要的组件
    /// </summary>
    private void InitializeComponents()
    {
        // 获取管理器实例
        templatePhotoManager = TemplatePhotoManager.Instance;
        cameraManager = WebCameraManager.Instance;
        tagDetector = AprilTagDetector.Instance;
        
        // 检查组件是否获取成功
        if (templatePhotoManager == null || cameraManager == null || tagDetector == null)
        {
            Debug.LogError("无法获取所需的管理器组件");
            enabled = false;
            return;
        }
        
        // 初始化相机
        cameraManager.InitializeCamera();
        
        // 设置相机预览
        if (previewRawImage != null)
        {
            cameraManager.SetPreviewImage(previewRawImage);
        }
        
        // 主动初始化AprilTag检测器
        tagDetector.InitializeDetector();
        
        // 注册输入处理
        RegisterInputHandlers();
        
        componentsInitialized = true;
        Debug.Log("所有组件初始化完成");
    }
    
    private void RegisterInputHandlers()
    {
        // 注册SimpleTouchForLite事件
        if (SimpleTouchForLite.Instance != null)
        {
            //SimpleTouchForLite.Instance.OnSimpleTap.AddListener(HandleTouchInput);
            SimpleTouchForLite.Instance.OnDoubleTap.AddListener(HandleDoubleTapExit);
            Debug.Log("已注册触摸事件监听");
        }
        else
        {
            Debug.LogWarning("SimpleTouchForLite实例未找到，触摸功能可能无法使用");
        }
    }

    void Update()
    {
        // 检查相机是否就绪，如果就绪且检测器未初始化，则初始化检测器
        if (componentsInitialized && cameraManager.IsCameraReady && tagDetector != null && !tagDetector.IsInitialized)
        {
            tagDetector.InitializeDetector();
            Debug.Log("相机就绪，初始化AprilTag检测器");
        }
    
        // 检测按键输入
        if (componentsInitialized && !isCapturing && 
            (Input.GetKeyDown(captureKeyCode1) || Input.GetKeyDown(captureKeyCode2)))
        {
            StartCaptureProcess();
        }
        
        // 显示当前检测到的AprilTag信息
        if (showDebugInfo && tagDetector != null && tagDetector.IsInitialized)
        {
            var tags = tagDetector.DetectedTags;
            if (tags.Length > 0)
            {
                string tagInfo = $"已检测到 {tags.Length} 个AprilTag: ";
                for (int i = 0; i < tags.Length; i++)
                {
                    tagInfo += $"ID={tags[i].ID} ";
                }
                Debug.Log(tagInfo);
            }
        }
    }

    void OnDestroy()
    {
        if (SimpleTouchForLite.SingletonExist)
        {
            //SimpleTouchForLite.Instance.OnSimpleTap.RemoveListener(HandleTouchInput);
            SimpleTouchForLite.Instance.OnDoubleTap.RemoveListener(HandleDoubleTapExit);
        }

        if (capturedTexture != null)
        {
            Destroy(capturedTexture);
        }
    }

    // // 处理触摸输入（单击拍照）
    // private void HandleTouchInput()
    // {
    //     if (componentsInitialized && !isCapturing)
    //     {
    //         StartCaptureProcess();
    //     }
    // }

    // 处理双击退出
    private void HandleDoubleTapExit()
    {
        #if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
        #else
        Application.Quit();
        #endif
    }

    [Button]
    private void StartCaptureProcess()
    {
        if (!componentsInitialized || isCapturing) return;
        
        Debug.Log("开始拍照...");
        isCapturing = true;
        CaptureAndSavePhoto();
    }

    private void CaptureAndSavePhoto()
    {
        if (!componentsInitialized)
        {
            Debug.LogError("组件未初始化，无法拍照");
            isCapturing = false;
            return;
        }

        try
        {
            // 使用AprilTagDetector处理拍照和Tag检测
            string photoName;
            Texture2D photoTexture;
            bool captureSuccess = tagDetector.CapturePhotoWithTag(out photoName, out photoTexture);
            
            if (!captureSuccess || photoTexture == null)
            {
                Debug.LogError("拍照过程失败");
                isCapturing = false;
                return;
            }
            
            // 保存捕获的纹理引用
            if (capturedTexture != null)
            {
                Destroy(capturedTexture);
            }
            capturedTexture = photoTexture;
            
            // 保存照片
            bool saveSuccess = templatePhotoManager.SaveTemplatePhoto(photoTexture, photoName);
            
            if (saveSuccess)
            {
                Debug.Log($"照片已成功保存: {photoName}");
                if (uiManager != null)
                {
                    string notificationMessage = "Photos save success";
                    // 检查是否识别到AprilTag
                    bool tagDetected = photoName.Contains("TagID=");
                    notificationMessage += "\n" + (tagDetected ? "AprilTag was identified" : "AprilTag was not identified");
                    
                    // 如果没有检测到AprilTag，使用红色背景
                    if (!tagDetected)
                    {
                        Color redColor = new Color(1f, 0f, 0f, 1f); // 纯红色
                        uiManager.ShowNotification(notificationMessage, redColor);
                    }
                    else
                    {
                        // 检测到AprilTag时使用绿色背景(RGB: 135, 200, 0)
                        Color greenColor = new Color(135f/255f, 200f/255f, 0f, 1f);
                        uiManager.ShowNotification(notificationMessage, greenColor);
                    }
                }
            }
            else
            {
                Debug.LogError($"保存照片失败: {photoName}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"拍照过程发生异常: {e.Message}");
        }
        finally
        {
            isCapturing = false;
        }
    }
} 